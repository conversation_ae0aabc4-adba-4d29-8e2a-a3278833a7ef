# OfferBlur - PDF脱敏处理系统

OfferBlur是一个用于处理PDF文件中敏感信息的脱敏系统，特别适用于对录取通知书（Offer）进行脱敏处理。

## 新功能更新

1. **多种文件处理方式**
   - 支持本地文件上传处理
   - 支持阿里云OSS文件处理
   - 支持文件夹批量处理（并行处理）
   - 提供Web用户界面，方便操作

2. **自定义OSS配置**
   - 可在Web界面直接配置OSS参数
   - 无需修改环境文件

3. **配置项集中管理**
   - 关键词检测
   - 敏感信息模式
   - 提示词模板

4. **并行处理能力**
   - 支持文件夹批量处理
   - 默认30个并行任务
   - 可自定义并行数量

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动Web界面

```bash
python main.py --web
```

或直接运行（无参数也会启动Web界面）:

```bash
python main.py
```

### 2. 命令行使用

使用本地文件：
```bash
python main.py --local_file path/to/your/file.pdf
```

使用OSS链接：
```bash
python main.py --oss_link https://your-bucket.oss-cn-hangzhou.aliyuncs.com/your-file.pdf
```

批量处理OSS链接：
```bash
python main.py --batch_file path/to/your/links.txt
```

处理文件夹中的所有PDF文件：
```bash
python main.py --directory path/to/your/folder
```

自定义并行处理数量：
```bash
python main.py --directory path/to/your/folder --max_workers 50
```

## 环境变量配置

在项目根目录创建`.env`文件，参考`.env.example`填写：

```
# 阿里云通义千问API密钥
DASHSCOPE_API_KEY=your_api_key_here

# 阿里云OSS配置
OSS_ACCESS_KEY_ID=your_access_key_id
OSS_ACCESS_KEY_SECRET=your_access_key_secret
OSS_ENDPOINT=oss-cn-hangzhou.aliyuncs.com
OSS_BUCKET_NAME=your_bucket_name
```

## Web界面功能

Web界面提供三种处理方式：

1. **本地文件上传**
   - 直接上传本地PDF文件进行处理

2. **阿里云OSS（使用默认配置）**
   - 使用环境变量中的OSS配置处理文件
   - 支持单个文件处理和批量处理

3. **阿里云OSS（自定义配置）**
   - 在界面上直接填写OSS配置参数
   - 适合临时使用不同的OSS配置

处理完成后，系统会显示处理结果并展示脱敏后的图像。所有处理后的文件都保存在output目录中。

## 配置文件

敏感词检测和提示词等配置已集中放在`config.py`文件中，可以根据需要进行修改。

主要配置项包括：
- 录取页面关键词列表
- 判断录取页面的提示词
- 敏感信息检测提示词
- 敏感信息正则表达式模式

## 注意事项

1. 本地文件上传仅支持PDF格式
2. 使用阿里云OSS需要正确配置相关参数
3. 脱敏后的文件将保存在output目录中
4. 文件夹处理时会递归搜索所有子目录中的PDF文件
5. 并行处理数量建议根据系统性能和内存情况调整
