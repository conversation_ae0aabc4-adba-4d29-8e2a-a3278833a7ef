import argparse
import logging
import os
from offer_blur import OfferBlur
import concurrent.futures
from typing import List, Dict
import gc

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(filename)s] [%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


def process_single_file(file_path):
    """处理单个文件"""
    blur = None
    try:
        blur = OfferBlur()
        result = blur.process_pdf(file_path)
        return result
    except Exception as e:
        return {
            "file_path": str(file_path),
            "status": "failed",
            "error": str(e),
            "output_path": None
        }
    finally:
        # 确保资源被释放
        if blur:
            del blur
        # 强制垃圾回收
        gc.collect()


def batch_process_files(file_paths: List[str], max_workers: int = 5) -> List[Dict]:
    """
    批量处理多个PDF文件，供index.py调用

    :param file_paths: 文件路径列表
    :param max_workers: 最大并行工作线程数
    :return: 处理结果列表，每个结果包含文件路径、成功状态和相关信息
    """
    logger.info(f"开始批量处理 {len(file_paths)} 个文件")

    # 使用线程池并行处理文件
    results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        future_to_file = {executor.submit(process_single_file, file_path): file_path
                          for file_path in file_paths}

        for future in concurrent.futures.as_completed(future_to_file):
            file_path = future_to_file[future]
            try:
                result = future.result()
                logger.info(f"文件 {file_path}: 处理{'成功' if result['status'] == 'success' else '失败'}")
                results.append(result)
            except Exception as e:
                logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
                results.append({
                    "file_path": str(file_path),
                    "status": "failed",
                    "error": f"线程执行异常: {str(e)}",
                    "output_path": None
                })

    # 统计处理结果
    success_count = sum(1 for r in results if r['status'] == 'success')
    logger.info(f"批量处理完成: 成功 {success_count}/{len(file_paths)}")

    return results


def batch_process_files_without_pool(file_paths: List[str]):
    """流式处理多个PDF文件"""
    logger.info(f"开始流式处理 {len(file_paths)} 个文件")

    results = []
    for file_path in file_paths:
        try:
            # 处理单个文件
            result = process_single_file(file_path)
            logger.info(f"文件 {file_path}: 处理{'成功' if result['status'] == 'success' else '失败'}")
            results.append(result)
        except Exception as e:
            logger.error(f"处理文件 {file_path} 时出错: {str(e)}")
            results.append({
                "file_path": str(file_path),
                "status": "failed",
                "error": f"执行异常: {str(e)}",
                "output_path": None
            })

    # 统计处理结果
    success_count = sum(1 for r in results if r['status'] == 'success')
    logger.info(f"批量处理完成: 成功 {success_count}/{len(file_paths)}")

    return results


def process_directory(directory_path: str, max_workers: int = 2) -> List[Dict]:
    """
    处理指定目录下的所有PDF文件

    :param directory_path: 目录路径
    :param max_workers: 最大并行工作线程数
    :return: 处理结果列表，每个结果包含文件路径、成功状态和相关信息
    """
    try:
        # 获取目录下所有PDF文件
        pdf_files = []
        for root, _, files in os.walk(directory_path):
            for file in files:
                if file.lower().endswith('.pdf'):
                    pdf_files.append(os.path.join(root, file))

        if not pdf_files:
            return [{
                "file_path": directory_path,
                "status": "failed",
                "error": "目录中没有找到PDF文件",
                "output_path": None
            }]

        # 使用线程池并行处理文件
        return batch_process_files(pdf_files, max_workers)
    except Exception as e:
        return [{
            "file_path": directory_path,
            "status": "failed",
            "error": f"处理目录失败: {str(e)}",
            "output_path": None
        }]


def main():
    parser = argparse.ArgumentParser(description='OfferBlur - PDF脱敏处理系统')
    parser.add_argument('--local_file', type=str, help='本地PDF文件路径')
    parser.add_argument('--directory', type=str, help='要处理的PDF文件目录路径')
    parser.add_argument('--max_workers', type=int, default=30, help='并行处理的最大线程数（默认30）')

    args = parser.parse_args()

    # 如果没有提供任何参数，显示帮助信息
    if not any([args.local_file, args.directory]):
        parser.print_help()
        logger.info("\n注意：Web界面功能已禁用，请使用命令行参数")
        return

    # 命令行处理逻辑
    if args.directory:
        # 处理目录下的所有PDF文件
        results = process_directory(args.directory, args.max_workers)

        # 统计处理结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        logger.info(f"目录处理完成: 成功 {success_count}/{len(results)}")

        # 输出失败的任务
        failed = [r for r in results if r['status'] == 'failed']
        if failed:
            logger.info("\n失败的任务:")
            for f in failed:
                logger.info(f"文件: {f['file_path']}, 错误: {f.get('error')}")

    elif args.local_file:
        # 处理本地文件
        blur = OfferBlur()
        result = blur.process_pdf(args.local_file)
        if result['status'] == 'success':
            logger.info(f"处理成功，输出文件: {result['output_path']}")
        else:
            logger.info(f"处理失败，原因: {result['error']}")


if __name__ == '__main__':
    main()
