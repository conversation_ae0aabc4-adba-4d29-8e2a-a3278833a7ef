import functools
import logging
import traceback
import mimetypes
from pathlib import Path
from typing import Dict, Union, Tuple
import fitz
import time
from PIL import Image, ImageDraw, ImageFilter
import re
import json
import os
import io
import tempfile
from contextlib import contextmanager
from dashscope import Application
import gc
import ocrmypdf
import json_repair

from config import (
    OPENAI_API_KEY,
    APPLICATION_ID,
    MAX_FILE_SIZE_MB,
    INPUT_PATH,
    OUTPUT_PATH,
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(filename)s] [%(funcName)s] - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)

# --- Constants ---
# SUPPORTED_LANGUAGES = "eng+chi_sim+jpn+kor+deu+fra+spa"
# SUPPORTED_LANGUAGES = "chi_sim"
SUPPORTED_LANGUAGES = "chi_sim+jpn+kor+eng+deu+fra+spa"
OCR_RETRY_LIMIT = 1  # Limit OCR attempts per file


class OfferBlurError(Exception):
    """自定义异常基类"""
    pass


class FileValidationError(OfferBlurError):
    """文件验证相关错误"""
    pass


class ProcessingError(OfferBlurError):
    """处理过程相关错误"""
    pass


class OfferBlur:
    def __init__(self):
        """
        初始化 OfferBlur 对象
        """
        # 验证配置
        self._validate_config()
        # Track OCR attempts to prevent infinite loops if OCR fails repeatedly
        self._ocr_attempts: Dict[Path, int] = {}

    def _validate_config(self):
        """验证配置是否有效"""
        if not OPENAI_API_KEY:
            raise ValueError("已添加OCR功能_OpenAI API密钥未配置")
        if not APPLICATION_ID:
            raise ValueError("已添加OCR功能_应用ID未配置")

    def _validate_file(self, file_path: Union[str, Path]) -> Tuple[Path, str]:
        """
        验证输入文件

        Args:
            file_path: 文件路径

        Returns:
            Tuple[Path, str]: 验证后的文件路径对象和MIME类型

        Raises:
            FileValidationError: 当文件验证失败时
        """
        safe_path_str = os.path.join(INPUT_PATH, str(file_path))
        logger.info(f"开始验证文件: {safe_path_str}")

        if not os.path.exists(safe_path_str):
            raise FileValidationError(f"已添加OCR功能_文件不存在: {safe_path_str}")

        file_path_obj = Path(safe_path_str)
        logger.info(f"文件已存在: {file_path_obj}")

        # 检查文件类型
        mime_type, _ = mimetypes.guess_type(str(file_path_obj))
        if not mime_type:
            raise FileValidationError(f"已添加OCR功能_无法确定文件类型: {file_path_obj}")

        # 允许 PDF 和常见的图片类型
        allowed_mime_starts = ('application/pdf', 'image/')
        if not any(mime_type.startswith(prefix) for prefix in allowed_mime_starts):
            logger.error(f"只支持 PDF 和图片,不支持的文件类型: {mime_type},文件路径: {file_path_obj}")
            raise FileValidationError(f"已添加OCR功能_只支持PDF和图片,不支持的文件类型: {mime_type}")

        # 检查文件大小
        file_size_mb = file_path_obj.stat().st_size / (1024 * 1024)
        logger.info(f"文件大小是: {file_size_mb} MB, MIME类型: {mime_type},文件路径: {file_path_obj}")
        # if file_size_mb > MAX_FILE_SIZE_MB:
        #     raise FileValidationError(f"文件大小超过限制: {file_size_mb:.2f}MB > {MAX_FILE_SIZE_MB}MB")

        return file_path_obj, mime_type

    @contextmanager
    def _safe_open_pdf(self, pdf_path: Path):
        """安全地打开PDF文件"""
        doc = None
        try:
            doc = fitz.open(pdf_path)
            yield doc
        except RuntimeError as e:
            logger.error(f"无法打开PDF文件 {pdf_path}: {str(e)}")
            raise ProcessingError(f"已添加OCR功能_无法打开PDF文件 {pdf_path}: {str(e)}") from e
        finally:
            if doc:
                doc.close()

    def _convert_image_to_pdf(self, image_path: Path) -> Path:
        """将图片文件转换为临时的单页PDF"""
        temp_pdf_path = None
        try:
            logger.info(f"开始将图片 {image_path} 转换为PDF")
            img_doc = fitz.open(str(image_path))
            if not img_doc or img_doc.page_count == 0:
                raise ProcessingError(f"已添加OCR功能_无法使用fitz打开图片文件: {image_path}")

            pdf_bytes = img_doc[0].get_pixmap().tobytes("png")  # Convert first page (image) to png bytes
            img_doc.close()  # Close image doc

            # Create a PDF page from image bytes
            page_doc = fitz.open()  # New empty PDF
            page_rect = fitz.Rect(0, 0, *Image.open(io.BytesIO(pdf_bytes)).size)  # Get image dims
            page = page_doc.new_page(width=page_rect.width, height=page_rect.height)
            page.insert_image(page_rect, stream=pdf_bytes)

            # Save to a temporary PDF file
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_pdf:
                temp_pdf_path = Path(temp_pdf.name)
                page_doc.save(temp_pdf.name, garbage=4, deflate=True)
            page_doc.close()

            logger.info(f"图片成功转换为临时PDF: {temp_pdf_path}")
            return temp_pdf_path
        except Exception as e:
            logger.error(f"图片转PDF失败 ({image_path}): {str(e)}")
            if temp_pdf_path and temp_pdf_path.exists():
                os.remove(temp_pdf_path)  # Clean up failed temp file
            raise ProcessingError(f"已添加OCR功能_图片转PDF失败: {str(e)}") from e

    def _run_ocr(self, input_pdf_path: Path, output_pdf_path: Path):
        """对PDF文件运行OCR"""
        try:
            # Check OCR attempt count
            attempt_count = self._ocr_attempts.get(input_pdf_path, 0)
            if attempt_count >= OCR_RETRY_LIMIT:
                logger.error(f"OCR达到重试上限 ({OCR_RETRY_LIMIT})，跳过文件: {input_pdf_path}")
                raise ProcessingError(f"已添加OCR功能_OCR达到重试上限，无法处理: {input_pdf_path.name}")

            self._ocr_attempts[input_pdf_path] = attempt_count + 1  # Increment before attempt

            logger.info(f"开始对 {input_pdf_path} 进行 OCR，输出到 {output_pdf_path}，语言: {SUPPORTED_LANGUAGES}")
            ocrmypdf.ocr(
                input_pdf_path,
                output_pdf_path,
                language=SUPPORTED_LANGUAGES,
                force_ocr=True,
                skip_text=False,
                progress_bar=False,
                output_type='pdf',
                rotate_pages = True,
                deskew=True,
                optimize=0,
            )
            logger.info(f"OCR完成: {output_pdf_path}")
        except ocrmypdf.exceptions.PriorOcrFoundError:
            logger.warning(f"文件 {input_pdf_path} 似乎已经包含OCR层，但我们仍强制重新OCR。")
            pass
        except ocrmypdf.exceptions.InputFileError as e:
            logger.error(f"OCR输入文件错误 ({input_pdf_path}): {str(e)}")
            raise ProcessingError(f"已添加OCR功能_OCR 输入文件无效: {str(e)}") from e
        except ocrmypdf.exceptions.MissingDependencyError as e:
            logger.error(f"OCR 缺少依赖: {str(e)}. 请确保安装了所有必需的语言包 (tesseract-lang).")
            raise ProcessingError(f"已添加OCR功能_OCR缺少依赖: {str(e)}") from e
        except Exception as e:
            logger.error(f"OCR处理失败,开始调整参数重试。详情： ({input_pdf_path}): {str(e)}")
            if output_pdf_path.exists():
                try:
                    os.remove(output_pdf_path)
                    logger.info(f"已删除不完整的OCR输出文件: {output_pdf_path}")
                except OSError as remove_err:
                    logger.error(f"无法删除不完整的OCR输出文件 {output_pdf_path}: {remove_err}")
            else:
                try:
                    ocrmypdf.ocr(
                        input_pdf_path,
                        output_pdf_path,
                        language=SUPPORTED_LANGUAGES,
                        force_ocr=True,
                        skip_text=False,
                        progress_bar=False,
                        output_type='pdf',
                        optimize=0,
                    )
                except Exception as e:
                    logger.error(f"运行 OCR 时发生未知错误 ({input_pdf_path}): {str(e)}")
                    logger.error(f"Traceback:\n{traceback.format_exc()}")
                    raise ProcessingError(f"已添加OCR功能_OCR处理失败: {str(e)}") from e

    def _process_ocr_pdf(self, doc, page_num, current_page, text, ocr_performed, original_input_path_str):
        """处理OCR后的PDF页面，包括敏感信息检测和打码"""
        try:
            # 检测敏感信息
            sensitive_areas = self.detect_sensitive_info(text)

            if sensitive_areas:
                logger.info(f"在第 {page_num + 1} 页检测到 {len(sensitive_areas)} 个敏感区域")

                # 使用当前页面（可能是OCR后的页面）进行渲染
                pix = current_page.get_pixmap(matrix=fitz.Matrix(100 / 72, 100 / 72))

                try:
                    output_jpg_path_str = self._get_output_path(original_input_path_str)
                    output_jpg_path = Path(output_jpg_path_str)

                    with Image.frombytes("RGB", [pix.width, pix.height], pix.samples) as image:
                        if ocr_performed:
                            processed_image = self.apply_frosted_glass_for_ocr(image, sensitive_areas, current_page)
                        else:
                            processed_image = self.apply_frosted_glass(image, sensitive_areas, current_page)
                        processed_image.save(output_jpg_path_str, optimize=True, quality=85)
                        logger.info(f"已保存打码后的第 {page_num + 1} 页图片: {output_jpg_path_str}")

                        output_size_mb = output_jpg_path.stat().st_size / (1024 * 1024)
                        logger.info(f"输出JPG文件大小: {output_size_mb:.2f}MB")

                    del processed_image

                finally:
                    del pix
                    if 'image' in locals(): del image
                    if 'processed_image' in locals(): del processed_image
                    gc.collect()

                return True, output_jpg_path
            else:
                logger.info(f"第 {page_num + 1} 页未检测到敏感信息")
                return False, None

        except Exception as e:
            logger.error(f"处理第 {page_num + 1} 页时出错: {str(e)}")
            logger.error(traceback.format_exc())
            raise ProcessingError(f"已添加OCR功能_处理页面 {page_num + 1} 失败: {str(e)}") from e

    def process_pdf(self, file_path: Union[str, Path]) -> Dict:
        """
        处理单个文件（PDF或图片）

        Args:
            file_path: 文件路径 (相对于 INPUT_PATH)

        Returns:
            Dict: 处理结果，包含文件路径、成功状态和相关信息
        """
        start_time = time.time()
        output_jpg_path = None
        temp_pdf_to_delete: Path | None = None
        ocr_output_path: Path | None = None
        original_input_path_str = str(file_path)
        current_file_to_process: Path | None = None

        try:
            # 1. 验证输入文件
            validated_path, mime_type = self._validate_file(original_input_path_str)
            current_file_to_process = validated_path
            logger.info(f"文件 {current_file_to_process} ({mime_type}) 验证通过")

            # --- OCR Handling ---
            ocr_performed = False
            is_pdf = False

            # 2. 如果是图片，转换为PDF并进行OCR
            if mime_type.startswith('image/'):
                logger.info(f"检测到图片文件，开始转换并OCR: {current_file_to_process}")
                # a. Convert image to temporary PDF
                temp_image_pdf = self._convert_image_to_pdf(current_file_to_process)
                temp_pdf_to_delete = temp_image_pdf

                # b. Define OCR output path
                ocr_output_path = current_file_to_process.with_suffix('.ocr.pdf')

                # c. Run OCR on the temporary PDF
                self._run_ocr(temp_image_pdf, ocr_output_path)

                # d. Update the file to process to the OCR'd PDF
                current_file_to_process = ocr_output_path
                ocr_performed = True
                logger.info(f"图片OCR完成，继续处理: {current_file_to_process}")

            # 3. 如果是PDF，直接进入处理流程
            elif mime_type == 'application/pdf':
                is_pdf = True
                logger.info(f"开始处理PDF文件: {current_file_to_process}")
                with self._safe_open_pdf(current_file_to_process) as doc:
                    if doc.page_count == 0:
                        logger.warning(f"PDF 文件为空: {current_file_to_process}")
                        return {
                            "file_path": original_input_path_str,
                            "status": "failed",
                            "error": "empty PDF",
                            "output_path": None
                        }

                    # --- Sensitive Info Detection and Blurring ---
                    found_sensitive_info = False
                    processed_page = False

                    for page_num, page in enumerate(doc):
                        if page_num >= 3 and not found_sensitive_info:
                            logger.info(f"前3页未检测到敏感信息，假定不是Offer，跳过剩余页面: {current_file_to_process}")
                            break

                        try:
                            # 尝试提取文本
                            text = page.get_text("text",sort=True)


                            # 获取页面上的图像数量
                            images = page.get_images(full=True)

                            # 判断页面类型
                            if text.strip() and len(images) == 0:
                                print("该页主要是文本")
                            elif not text.strip() and len(images) > 0:
                                print("该页是图像页（或扫描页）")
                            elif text.strip() and len(images) > 0:
                                print("该页是图文混合页")
                            else:
                                print("该页是空白页")


                            # 默认使用原始页面
                            current_page = page
                            logger.info(f"{current_file_to_process}提取到的文本：\n {text}")
                            # 如果文本为空或者存在乱码，尝试对该页进行OCR
                            if not text or '�' in text:
                                logger.info(f"第 {page_num + 1} 页没有提取到文本，尝试OCR")
                                # 创建临时文件用于OCR
                                with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_pdf:
                                    temp_page_pdf = Path(temp_pdf.name)
                                    temp_doc = fitz.open()
                                    temp_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
                                    temp_doc.save(str(temp_page_pdf))
                                    temp_doc.close()

                                # 对单页进行OCR
                                ocr_page_pdf = temp_page_pdf.with_name(f"{temp_page_pdf.stem}_ocr.pdf")
                                self._run_ocr(temp_page_pdf, ocr_page_pdf)

                                # 使用OCR后的PDF页面
                                ocr_doc = fitz.open(str(ocr_page_pdf))
                                current_page = ocr_doc[0]
                                text = current_page.get_text("text")
                                logger.info(f"{current_file_to_process}ocr转换后提取到的文本：\n {text}")
                                # 清理临时文件
                                temp_page_pdf.unlink()
                                ocr_page_pdf.unlink()

                                ocr_performed = True
                                logger.info(f"第 {page_num + 1} 页OCR完成")

                            # 如果仍然没有文本，跳过该页
                            if not text:
                                logger.warning(f"第 {page_num + 1} 页OCR后仍然没有文本，跳过该页")
                                continue

                            # 处理OCR后的页面
                            logger.info(f"开始处理PDF文件的第 {page_num + 1} 页")
                            found_sensitive_info, output_jpg_path = self._process_ocr_pdf(
                                doc, page_num, current_page, text, ocr_performed, original_input_path_str
                            )

                            if found_sensitive_info:
                                processed_page = True
                                logger.info(f"已处理并保存第一个包含敏感信息的页面 ({page_num + 1})，停止处理此文件。")
                                break

                        except Exception as e:
                            logger.error(f"处理第 {page_num + 1} 页时出错: {str(e)}")
                            logger.error(traceback.format_exc())
                            raise ProcessingError(f"已添加OCR功能_处理页面 {page_num + 1} 失败: {str(e)}") from e

            # 4. 如果是OCR后的PDF（来自图片），继续处理
            if not is_pdf and ocr_performed and current_file_to_process:
                logger.info(f"开始处理图片OCR后的PDF文件: {current_file_to_process}")
                with self._safe_open_pdf(current_file_to_process) as doc:
                    if doc.page_count > 0:
                        page = doc[0]  # 只处理第一页
                        text = page.get_text("text")
                        logger.info(f"{current_file_to_process} OCR后提取到的文本：\n {text}")
                        if text:
                            found_sensitive_info, output_jpg_path = self._process_ocr_pdf(
                                doc, 0, page, text, ocr_performed, original_input_path_str
                            )
                            processed_page = found_sensitive_info

            elapsed_time = time.time() - start_time
            logger.info(f"{original_input_path_str} 总处理时间: {elapsed_time:.2f}秒")

            if not processed_page:
                if not found_sensitive_info:
                    return {
                        "file_path": original_input_path_str,
                        "status": "failed",
                        "error": "no sensitive info",
                        "output_path": None
                    }
                else:
                    return {
                        "file_path": original_input_path_str,
                        "status": "failed",
                        "error": "sensitive info found but output generation failed",
                        "output_path": None
                    }

            relative_output_path = os.path.relpath(output_jpg_path, OUTPUT_PATH) if output_jpg_path else None
            return {
                "file_path": original_input_path_str,
                "status": "success",
                "error": None,
                "output_path": relative_output_path
            }

        except (FileValidationError, ProcessingError) as e:
            elapsed_time = time.time() - start_time
            logger.error(f"处理 {original_input_path_str} 时发生错误: {str(e)}")
            logger.info(f"总处理时间: {elapsed_time:.2f}秒")
            return {
                "file_path": original_input_path_str,
                "status": "failed",
                "error": str(e),
                "output_path": None
            }
        except Exception as e:
            elapsed_time = time.time() - start_time
            logger.error(f"处理 {original_input_path_str} 时发生意外异常: {str(e)}")
            logger.error(traceback.format_exc())
            logger.info(f"总处理时间: {elapsed_time:.2f}秒")
            return {
                "file_path": original_input_path_str,
                "status": "failed",
                "error": f"意外错误: {str(e)}",
                "output_path": None
            }
        finally:
            # 清理临时文件
            if temp_pdf_to_delete and temp_pdf_to_delete.exists():
                try:
                    os.remove(temp_pdf_to_delete)
                    logger.info(f"已删除临时文件: {temp_pdf_to_delete}")
                except OSError as e:
                    logger.error(f"无法删除临时文件 {temp_pdf_to_delete}: {str(e)}")

            if ocr_output_path and ocr_output_path.exists():
                try:
                    os.remove(ocr_output_path)
                    logger.info(f"已删除OCR输出文件: {ocr_output_path}")
                except OSError as e:
                    logger.error(f"无法删除OCR输出文件 {ocr_output_path}: {str(e)}")

            if 'validated_path' in locals() and validated_path in self._ocr_attempts:
                del self._ocr_attempts[validated_path]

            gc.collect()

    def batch_process(self, file_paths: list[Union[str, Path]]) -> list[Dict]:
        """
        批量处理多个文件 (PDF或图片)

        Args:
            file_paths: 文件路径列表 (相对于 INPUT_PATH)

        Returns:
            list[Dict]: 处理结果列表
        """
        results = []
        # Reset OCR attempts at the start of a batch
        self._ocr_attempts = {}
        for path in file_paths:
            try:
                result = self.process_pdf(path)
                results.append(result)
            except Exception as e:
                # Catch any unexpected errors during the process_pdf call itself
                logger.error(f"批处理中处理 {path} 时发生顶层异常: {str(e)}")
                logger.error(traceback.format_exc())
                results.append({
                    "file_path": str(path),
                    "status": "failed",
                    "error": f"批处理中未预期的异常: {str(e)}",
                    "output_path": None
                })
            finally:
                # Ensure GC runs after each file in the batch
                gc.collect()
        return results

    def detect_sensitive_info(self, text):
        """检测敏感信息及其坐标"""
        # Keep only the first 7000 characters as before
        text_slice = text[:7000] if text else ""
        if not text_slice:
            logger.warning("文本为空，无法检测敏感信息。")
            return []  # Return empty list for no text

        try:
            # 调用千问API识别敏感信息
            sensitive_areas = self._detect_with_qwen(text_slice)  # Pass sliced text
            # logger.info(f"千问API识别敏感信息结果是: {sensitive_areas}") # Reduce log noise
            if sensitive_areas:
                return sensitive_areas
            return []  # Return empty list if API says no sensitive info
        except Exception as e:
            logger.error(f"检测敏感信息失败: {str(e)}")
            # Re-raise the exception to be caught by the main processor
            raise ProcessingError(f"已添加OCR功能_检测敏感信息API调用失败: {str(e)}") from e

    def _detect_with_qwen(self, text_slice):
        """使用百炼/千问 API检测敏感信息"""
        try:
            # logger.info(f"传递给百炼 API 的文本片段: {text_slice[:100]}...") # Log only beginning
            qwen_result = self.call_bailian(text_slice)

            # logger.info(f"千问API原始返回: {qwen_result}")
            if qwen_result["status_code"] != 200:
                # Log more details on error
                error_msg = qwen_result.get("message", "未知错误")
                request_id = qwen_result.get("request_id", "N/A")
                logger.error(
                    f"千问 API 返回错误 (Code: {qwen_result['status_code']}, RequestID: {request_id}): {error_msg}")
                raise Exception(
                    f"已添加OCR功能_千问API返回错误 (Code: {qwen_result['status_code']}, RequestID: {request_id}): {error_msg}")

            result_text = qwen_result.get("output", {}).get("text")
            if not result_text:
                logger.error(
                    f"千问 API 成功返回但未包含 'output.text' 内容。 RequestID: {qwen_result.get('request_id', 'N/A')}")
                raise Exception("已添加OCR功能_千问API成功返回但未包含 'output.text' 内容")

            result = result_text.strip()
            # logger.debug(f"千问API返回文本: {result}")

            # 从结果中提取JSON部分
            json_data = OfferBlur.extract_json_from_text(result)
            # logger.info(f"提取到的JSON数据 (原始): {json_data}")
            json_data = json_repair.repair_json(json_data,ensure_ascii=False)
            if json_data:
                try:
                    # Parse potentially nested JSON
                    processed_data = self.parse_nested_json(json_data)
                    logger.info(f"解析后的JSON数据: {processed_data}")

                    # Check for sensitive info flag and data
                    if processed_data and isinstance(processed_data, dict) and processed_data.get(
                            "has_sensitive_info") == "yes":
                        sensitive_info_list = processed_data.get("sensitive_info", [])
                        if isinstance(sensitive_info_list, list):
                            # Validate items have expected keys before returning
                            valid_items = [item for item in sensitive_info_list if
                                           isinstance(item, dict) and "type" in item and "content" in item]
                            if len(valid_items) != len(sensitive_info_list):
                                logger.warning("部分敏感信息条目格式无效，已过滤。")
                            logger.info(f"检测到 {len(valid_items)} 个有效敏感信息条目。")
                            return valid_items  # Return list of valid {"type": ..., "content": ...}
                        else:
                            logger.warning("敏感信息标记为 'yes' 但 'sensitive_info' 不是列表或缺失。")
                            return []
                    else:
                        logger.info("API返回结果表明无敏感信息。")
                        return []  # No sensitive info found according to API

                except json.JSONDecodeError as je:
                    logger.error(f"解析千问API返回的JSON失败: {je}, JSON内容: {json_data}")
                    raise Exception(f"已添加OCR功能_解析千问API返回的JSON失败: {je}") from je
                except Exception as pe:  # Catch errors during processing the parsed data
                    logger.error(f"处理解析后的JSON数据时出错: {pe}, 数据: {processed_data}")
                    raise Exception(f"已添加OCR功能_处理解析后的JSON数据时出错: {pe}") from pe
            else:
                logger.warning(f"未能在API返回中提取JSON: {result[:200]}...")
                # Decide how to handle non-JSON response - treat as no sensitive info? Or raise error?
                # Let's treat it as potentially problematic API response
                raise Exception("已添加OCR功能_未能从千问API响应中提取有效的JSON")

        except Exception as e:
            # Catch exceptions from call_bailian or processing logic
            logger.error(f"调用或处理 Qwen API 时出错: {str(e)}")
            # Avoid logging full traceback here if already logged elsewhere, but ensure error propagates
            if not isinstance(e, ProcessingError):  # Avoid wrapping ProcessingError again
                raise Exception(f"已添加OCR功能_调用或处理Qwen API失败: {str(e)}") from e
            else:
                raise e  # Re-raise ProcessingError or other specific exceptions

    def apply_blur(self, image, sensitive_areas, page):
        """(Deprecated - Using Frosted Glass) 对敏感区域进行打码处理"""
        logger.warning("apply_blur is deprecated, using apply_frosted_glass instead.")
        return self.apply_frosted_glass(image, sensitive_areas, page)

    @classmethod
    def parse_nested_json(cls, json_data):
        """
        递归解析包含嵌套JSON字符串的JSON数据

        Args:
            json_data: JSON字符串或已解析的Python对象

        Returns:
            完全解析后的Python对象，所有嵌套JSON字符串都会被解析
        """
        # 如果是字符串，尝试解析
        if isinstance(json_data, str):
            try:
                # Avoid infinite recursion on non-JSON strings that look like JSON starts
                if not (json_data.startswith('{') and json_data.endswith('}')) and \
                        not (json_data.startswith('[') and json_data.endswith(']')):
                    return json_data  # Not a likely JSON string

                parsed_data = json.loads(json_data)
                # 递归处理解析结果
                return OfferBlur.parse_nested_json(parsed_data)
            except json.JSONDecodeError:
                # 如果不是有效的JSON，保持原字符串不变
                return json_data
            except RecursionError:
                logger.error("解析嵌套JSON时达到递归深度限制。")
                return json_data  # Return original string on recursion error

        # 如果是字典，递归处理每个值
        elif isinstance(json_data, dict):
            return {key: OfferBlur.parse_nested_json(value) for key, value in json_data.items()}

        # 如果是列表，递归处理每个元素
        elif isinstance(json_data, list):
            return [OfferBlur.parse_nested_json(item) for item in json_data]

        # 其他类型（数字、布尔值、None）直接返回
        else:
            return json_data

    def call_bailian(self, text):
        """调用百炼/千问应用"""
        try:
            response = Application.call(
                api_key=OPENAI_API_KEY,
                app_id=APPLICATION_ID,
                # Ensure prompt is never empty, provide default if text is empty after slicing
                prompt=text if text else "没有文本内容",
                # Add timeout?
                # timeout=60
            )
            # Basic check on response structure
            if not isinstance(response, dict) or "status_code" not in response:
                logger.error(f"百炼 API 返回格式无效: {response}")
                # Simulate an error response structure
                return {"status_code": 500, "message": "API 响应格式无效", "output": None, "request_id": "N/A"}
            return response
        except Exception as e:
            logger.error(f"调用百炼 API 时发生网络或SDK错误: {str(e)}")
            # Return an error structure consistent with successful calls but with error status
            return {"status_code": 500, "message": f"API 调用失败: {str(e)}", "output": None, "request_id": "N/A"}

    @staticmethod
    def extract_json_from_text(llm_result,source=None):
        """
        从文本中提取JSON字符串，支持多种格式。优先匹配Markdown代码块。

        参数:
            text (str): 可能包含JSON的文本

        返回:
            str: 提取出的JSON字符串，若提取失败则返回None
        """
        # logger.info("开始从文本中提取JSON") # Reduce log noise

        if not llm_result:
            return None
        if source == "index":
            return json.dumps(json.loads(llm_result))
        # 策略1: 尝试从Markdown代码块中提取 (Most reliable)
        markdown_matches = re.findall(r'```(?:json)?\s*(\{.*?\})\s*```', llm_result, re.DOTALL)
        if not markdown_matches:  # Try matching arrays too
            markdown_matches = re.findall(r'```(?:json)?\s*(\[.*?\])\s*```', llm_result, re.DOTALL)

        for match in markdown_matches:
            try:
                candidate = match.strip()
                json.loads(candidate)  # Validate
                # logger.info("从Markdown代码块中成功提取JSON")
                return candidate
            except json.JSONDecodeError:
                continue  # Try next match if this one is invalid

        # logger.debug("Markdown JSON 未找到，尝试其他策略") # Reduce log noise

        # Strategy 2: Find first valid JSON object or array appearing in the text
        # More robust regex to find top-level JSON objects/arrays
        # It's hard to be perfect, but this aims for the first plausible structure.
        first_match = re.search(r'(\{.*?\})(?:\s|$)|(\[.*?\])(?:\s|$)', llm_result, re.DOTALL)
        if first_match:
            potential_json = first_match.group(1) or first_match.group(2)
            if potential_json:
                try:
                    json.loads(potential_json)
                    # logger.info("在文本中直接提取到第一个JSON对象/数组")
                    return potential_json
                except json.JSONDecodeError:
                    # logger.debug(f"找到的潜在JSON无效: {potential_json[:100]}...") # Reduce log noise
                    pass  # Continue searching

        # Strategy 3: If the entire string is JSON (less likely but possible)
        try:
            stripped_text = llm_result.strip()
            if (stripped_text.startswith('{') and stripped_text.endswith('}')) or \
                    (stripped_text.startswith('[') and stripped_text.endswith(']')):
                json.loads(stripped_text)
                # logger.info("整个文本内容是有效的JSON")
                return stripped_text
        except json.JSONDecodeError:
            pass

        # logger.warning("未能从文本中提取有效的JSON") # Reduce log noise
        # logger.debug(f"原始文本 (前200): {llm_result[:200]}...")
        return None

    def _get_output_path(self, original_input_path_str: str) -> str:
        """
        根据 *原始* 输入文件路径生成最终输出JPG文件路径。
        确保输出JPG文件名与初始输入文件（PDF或图片）对应，
        忽略任何中间步骤（如 _ocr.pdf 后缀）。

        Args:
            original_input_path_str: 原始输入文件的相对路径字符串 (e.g., 'subdir/myoffer.pdf' or 'subdir/image.png')

        Returns:
            str: 完整的输出 JPG 文件路径 (e.g., '/path/to/output/subdir/myoffer.jpg')
        """
        try:
            # Work with the relative path structure from the input
            relative_path = original_input_path_str

            # Get the directory part of the relative path
            relative_dir = os.path.dirname(relative_path)

            # Get the filename without extension from the original input path
            original_basename = os.path.basename(relative_path)
            file_stem = os.path.splitext(original_basename)[0]

            # Construct the output directory path under OUTPUT_PATH
            output_dir = os.path.join(OUTPUT_PATH, relative_dir)

            # Construct the final output JPG filename
            output_file = f"{file_stem}.jpg"  # Always use .jpg extension

            # Ensure the output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Combine directory and filename for the full output path
            full_output_path = os.path.join(output_dir, output_file)
            # logger.info(f"生成输出路径: {full_output_path} (基于原始输入: {original_input_path_str})")
            return full_output_path
        except Exception as e:
            logger.error(f"构建输出路径失败 (基于 {original_input_path_str}): {str(e)}")
            # Fallback: Save directly in OUTPUT_PATH with original stem + .jpg
            fallback_stem = os.path.splitext(os.path.basename(original_input_path_str))[0]
            fallback_path = os.path.join(OUTPUT_PATH, f"{fallback_stem}.jpg")
            logger.warning(f"使用回退输出路径: {fallback_path}")
            # Ensure base output path exists for fallback
            os.makedirs(OUTPUT_PATH, exist_ok=True)
            return fallback_path

    def apply_frosted_glass(self, image, sensitive_areas, page):
        """对敏感区域应用毛玻璃效果"""
        try:
            img_width, img_height = image.size
            # logger.info(f"图片尺寸: {img_width}x{img_height}") # Reduce log noise

            # Get text blocks only once per page if needed for fallback search
            text_blocks = None  # Lazy load

            # Create a copy of the image to draw on if blurring multiple areas
            # Otherwise, Pillow modifies the image in place
            output_image = image.copy()  # Work on a copy

            # Process each sensitive area
            for area in sensitive_areas:
                sensitive_text = str(area.get("content", "")).strip()  # Ensure string and strip whitespace
                area_type = area.get("type", "未知")

                if not sensitive_text:
                    logger.warning(f"敏感区域 '{area_type}' 内容为空，跳过。")
                    continue

                # logger.info(f"应用毛玻璃效果: 类型='{area_type}', 内容='{sensitive_text}'")

                try:
                    # Use PyMuPDF search (more reliable for exact matches)
                    text_instances = page.search_for(sensitive_text, quads=False)  # Get Rects
                    # logger.debug(f"找到 '{sensitive_text}' 的实例数: {len(text_instances)}")
                except Exception as search_err:
                    logger.warning(f"搜索文本 '{sensitive_text}' 时出错: {search_err}")
                    text_instances = []

                if not text_instances:
                    logger.warning(f"页面上未精确找到文本: '{sensitive_text}'. 尝试模糊块匹配。")
                    # Fallback: Try block matching (less precise)
                    if text_blocks is None:  # Load blocks only if needed
                        text_blocks = page.get_text("blocks")  # [(x0, y0, x1, y1, "text", block_no, block_type)]

                    found_in_block = False
                    for block in text_blocks:
                        block_text = block[4]
                        # Simple substring check (case-sensitive)
                        if sensitive_text in block_text:
                            logger.info(f"通过模糊匹配在块中找到 '{sensitive_text}': '{block_text[:50]}...'")
                            x0, y0, x1, y1 = block[:4]  # Block coordinates

                            # Convert PDF coordinates to image coordinates
                            scale_factor = 100 / 72  # Assuming 100 DPI image rendering
                            img_x0 = x0 * scale_factor
                            img_y0 = y0 * scale_factor
                            img_x1 = x1 * scale_factor
                            img_y1 = y1 * scale_factor

                            # Clamp coordinates to image bounds
                            img_x0 = max(0, min(img_x0, img_width))
                            img_y0 = max(0, min(img_y0, img_height))
                            img_x1 = max(0, min(img_x1, img_width))
                            img_y1 = max(0, min(img_y1, img_height))

                            # Apply effect to the *copied* image
                            self._apply_frosted_glass_effect(output_image,
                                                             (int(img_x0), int(img_y0), int(img_x1), int(img_y1)))
                            logger.info(f"模糊匹配打码区域: {area_type}, 坐标: {(img_x0, img_y0, img_x1, img_y1)}")
                            found_in_block = True
                            break  # Assume first block match is sufficient for this sensitive text
                    if not found_in_block:
                        logger.warning(f"文本 '{sensitive_text}' 在块中也未找到。")

                # Apply effect for each exact instance found
                for instance_rect in text_instances:
                    # Convert PDF Rect coordinates to image coordinates
                    scale_factor = 100 / 72  # Assuming 100 DPI rendering

                    img_x0 = instance_rect.x0 * scale_factor
                    img_y0 = instance_rect.y0 * scale_factor
                    img_x1 = instance_rect.x1 * scale_factor
                    img_y1 = instance_rect.y1 * scale_factor

                    # Clamp coordinates to image bounds
                    img_x0 = max(0, min(img_x0, img_width))
                    img_y0 = max(0, min(img_y0, img_height))
                    img_x1 = max(0, min(img_x1, img_width))
                    img_y1 = max(0, min(img_y1, img_height))

                    # Apply effect to the *copied* image
                    bbox = (int(img_x0), int(img_y0), int(img_x1), int(img_y1))
                    self._apply_frosted_glass_effect(output_image, bbox)
                    # logger.info(f"精确匹配打码区域: {area_type}, 坐标: {bbox}") # Reduce log noise

            # logger.info("所有敏感区域处理完成") # Reduce log noise
            return output_image  # Return the modified copy

        except Exception as e:
            logger.error(f"应用毛玻璃处理失败: {str(e)}")
            logger.error(traceback.format_exc())
            # Propagate error to be handled by the main processing loop
            raise ProcessingError(f"已添加OCR功能_应用毛玻璃效果失败: {str(e)}") from e

    def _apply_frosted_glass_effect(self, image, bbox):
        """应用毛玻璃效果到指定区域 (修改image inplace)"""
        x0, y0, x1, y1 = bbox

        # Ensure coordinates are valid integers and define a region
        x0, y0, x1, y1 = int(max(0, x0)), int(max(0, y0)), int(min(image.width, x1)), int(min(image.height, y1))

        # Get region dimensions
        width = x1 - x0
        height = y1 - y0

        if width <= 0 or height <= 0:
            logger.warning(f"无效或零尺寸区域: {(x0, y0, x1, y1)}，跳过毛玻璃处理")
            return  # Don't process empty or invalid regions

        try:
            # Crop the region to apply the filter
            region = image.crop((x0, y0, x1, y1))

            # Determine blur radius - make it proportional to the smallest dimension
            # Ensure a minimum and maximum radius for reasonable effect
            min_dim = min(width, height)
            blur_radius = max(5, min(25, min_dim // 8))  # Adjust divisor and bounds as needed

            # Apply Gaussian blur
            blurred_region = region.filter(ImageFilter.GaussianBlur(radius=blur_radius))

            # Paste the blurred region back into the image (modifies inplace)
            image.paste(blurred_region, (x0, y0))

        except Exception as e:
            logger.error(f"应用高斯模糊到区域 {bbox} 时出错: {str(e)}")
            raise ProcessingError(f"已添加OCR功能_应用高斯模糊到区域 {bbox} 时出错: {str(e)}") from e

    def apply_frosted_glass_for_ocr(self, image, sensitive_areas, ocr_page):
        """专门针对OCR页面的打码处理，扩大打码区域以确保完全覆盖"""
        try:
            img_width, img_height = image.size
            output_image = image.copy()

            # OCR 页面需要更大的扩展系数
            EXPANSION_RATIO = 1.2  # 区域扩展比例
            MIN_PADDING = 8  # 最小padding像素值

            for area in sensitive_areas:
                sensitive_text = str(area.get("content", "")).strip()
                area_type = area.get("type", "未知")

                if not sensitive_text:
                    continue

                try:
                    text_instances = []
                    # 1. 首先尝试精确搜索
                    exact_matches = ocr_page.search_for(sensitive_text, quads=True)
                    if exact_matches:
                        text_instances.extend(exact_matches)

                    # 2. 如果精确搜索失败，使用文本块搜索
                    if not text_instances:
                        blocks = ocr_page.get_text("blocks")
                        for block in blocks:
                            block_text = block[4]
                            if sensitive_text in block_text:
                                x0, y0, x1, y1 = block[:4]
                                text_instances.append(fitz.Quad(x0, y0, x1, y0, x0, y1, x1, y1))

                    # 3. 处理每个找到的实例
                    for instance in text_instances:
                        # 3.1 获取基础坐标
                        points = instance.rect
                        scale_factor = 100 / 72  # DPI转换

                        # 3.2 计算区域中心点
                        center_x = (points.x0 + points.x1) / 2
                        center_y = (points.y0 + points.y1) / 2

                        # 3.3 计算原始宽度和高度
                        original_width = points.x1 - points.x0
                        original_height = points.y1 - points.y0

                        # 3.4 计算扩展后的宽度和高度
                        expanded_width = max(original_width * EXPANSION_RATIO, original_width + MIN_PADDING)
                        expanded_height = max(original_height * EXPANSION_RATIO, original_height + MIN_PADDING)

                        # 3.5 从中心点计算新的坐标
                        new_x0 = center_x - (expanded_width / 2)
                        new_x1 = center_x + (expanded_width / 2)
                        new_y0 = center_y - (expanded_height / 2)
                        new_y1 = center_y + (expanded_height / 2)

                        # 3.6 转换为图像坐标并确保在边界内
                        img_x0 = max(0, int(new_x0 * scale_factor))
                        img_y0 = max(0, int(new_y0 * scale_factor))
                        img_x1 = min(img_width, int(new_x1 * scale_factor))
                        img_y1 = min(img_height, int(new_y1 * scale_factor))

                        # 3.7 应用模糊效果
                        region = output_image.crop((img_x0, img_y0, img_x1, img_y1))

                        # 计算模糊半径（基于区域大小）
                        blur_radius = max(10, min(int(min(expanded_width, expanded_height) * 0.2), 30))

                        # 应用模糊
                        blurred = region.filter(ImageFilter.GaussianBlur(radius=blur_radius))

                        # 粘贴回原图
                        output_image.paste(blurred, (img_x0, img_y0))

                        logger.info(
                            f"OCR模式：对区域 {area_type} 应用扩展打码效果: 原始大小 {original_width:.1f}x{original_height:.1f} -> "
                            f"扩展大小 {expanded_width:.1f}x{expanded_height:.1f}")

                except Exception as e:
                    logger.error(f"OCR模式处理区域失败: {str(e)}")
                    continue

            return output_image

        except Exception as e:
            logger.error(f"OCR模式打码处理完全失败: {str(e)}")
            logger.error(traceback.format_exc())
            raise ProcessingError(f"已添加OCR功能_OCR模式打码处理失败: {str(e)}") from e
